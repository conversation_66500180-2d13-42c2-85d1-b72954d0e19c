# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Keep WebView JavaScript interfaces
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep WebView related classes
-keep public class android.webkit.WebView
-keep public class android.webkit.WebViewClient

# Preserve the line number information for debugging stack traces
-keepattributes SourceFile,LineNumberTable

# Keep file names but hide the original source file name
-renamesourcefileattribute SourceFile

# Keep Compose related classes
-keep class androidx.compose.** { *; }
-keep class androidx.activity.compose.** { *; }

# Keep Google Ads related classes
-keep class com.google.android.gms.ads.** { *; }

# General Android rules
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep the app's entry points
-keep class com.mdmusfikurrahaman.travelessentialshub.MainActivity { *; }