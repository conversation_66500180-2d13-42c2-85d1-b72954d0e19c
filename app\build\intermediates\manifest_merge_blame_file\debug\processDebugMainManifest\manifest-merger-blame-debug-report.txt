1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdmusfikurrahaman.travelessentialshub"
4    android:versionCode="4"
5    android:versionName="2.2" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:6:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:25:5-79
12-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:25:22-76
13    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
13-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:5-79
13-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:22-76
14    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
14-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
14-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
15-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
15-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
16-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
16-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
17    <queries>
17-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
18
19        <!-- For browser content -->
20        <intent>
20-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
21            <action android:name="android.intent.action.VIEW" />
21-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
21-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
23-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
24
25            <data android:scheme="https" />
25-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
25-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
26        </intent>
27        <!-- End of browser content -->
28        <!-- For CustomTabsService -->
29        <intent>
29-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
30            <action android:name="android.support.customtabs.action.CustomTabsService" />
30-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
30-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
31        </intent>
32        <!-- End of CustomTabsService -->
33    </queries>
34
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
35-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
36-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
37
38    <permission
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
39        android:name="com.mdmusfikurrahaman.travelessentialshub.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.mdmusfikurrahaman.travelessentialshub.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:8:5-37:19
45        android:allowBackup="false"
45-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:9:9-36
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:10:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:11:9-54
51        android:icon="@mipmap/ic_launcher"
51-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:12:9-43
52        android:label="@string/app_name"
52-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:13:9-41
53        android:roundIcon="@mipmap/ic_launcher_round"
53-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:14:9-54
54        android:supportsRtl="true"
54-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:15:9-35
55        android:testOnly="true"
56        android:theme="@style/Theme.TravelEssentialsHub" >
56-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:16:9-57
57        <meta-data
57-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:19:9-21:69
58            android:name="com.google.android.gms.ads.APPLICATION_ID"
58-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:20:13-69
59            android:value="ca-app-pub-2281902770675036~1234567890" />
59-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:21:13-67
60
61        <activity
61-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:23:9-36:20
62            android:name="com.mdmusfikurrahaman.travelessentialshub.MainActivity"
62-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:24:13-41
63            android:alwaysRetainTaskState="false"
63-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:28:13-50
64            android:clearTaskOnLaunch="true"
64-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:29:13-45
65            android:exported="true"
65-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:25:13-36
66            android:label="@string/app_name"
66-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:26:13-45
67            android:noHistory="true"
67-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:27:13-37
68            android:theme="@style/Theme.TravelEssentialsHub" >
68-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:30:13-61
69            <intent-filter>
69-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:31:13-35:29
70                <action android:name="android.intent.action.MAIN" />
70-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:32:17-69
70-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:32:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:34:17-77
72-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:34:27-74
73            </intent-filter>
74        </activity>
75        <activity
75-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
76            android:name="androidx.compose.ui.tooling.PreviewActivity"
76-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
77            android:exported="true" />
77-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
78        <activity
78-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
79            android:name="androidx.activity.ComponentActivity"
79-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
80            android:exported="true" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
80-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
81        <activity
81-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
82            android:name="com.google.android.gms.ads.AdActivity"
82-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
83            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
83-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
84            android:exported="false"
84-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
85            android:theme="@android:style/Theme.Translucent" />
85-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
86
87        <provider
87-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
88            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
88-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
89            android:authorities="com.mdmusfikurrahaman.travelessentialshub.mobileadsinitprovider"
89-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
90            android:exported="false"
90-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
91            android:initOrder="100" />
91-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
92
93        <service
93-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
94            android:name="com.google.android.gms.ads.AdService"
94-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
95            android:enabled="true"
95-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
96            android:exported="false" />
96-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
97
98        <activity
98-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
99            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
99-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
100            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
100-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
101            android:exported="false" />
101-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
102        <activity
102-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
103            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
103-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
104            android:excludeFromRecents="true"
104-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
105            android:exported="false"
105-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
106            android:launchMode="singleTask"
106-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
107            android:taskAffinity=""
107-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
108            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
108-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
109
110        <property
110-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
111            android:name="android.adservices.AD_SERVICES_CONFIG"
111-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
112            android:resource="@xml/gma_ad_services_config" />
112-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
113
114        <activity
114-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
115            android:name="com.google.android.gms.common.api.GoogleApiActivity"
115-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
116            android:exported="false"
116-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
117            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
117-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
118
119        <meta-data
119-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
120            android:name="com.google.android.gms.version"
120-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
121            android:value="@integer/google_play_services_version" />
121-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
122
123        <provider
123-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
124            android:name="androidx.startup.InitializationProvider"
124-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
125            android:authorities="com.mdmusfikurrahaman.travelessentialshub.androidx-startup"
125-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
126            android:exported="false" >
126-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
127            <meta-data
127-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
128                android:name="androidx.work.WorkManagerInitializer"
128-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
129                android:value="androidx.startup" />
129-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
130            <meta-data
130-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.emoji2.text.EmojiCompatInitializer"
131-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
132                android:value="androidx.startup" />
132-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
133            <meta-data
133-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
134-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
135                android:value="androidx.startup" />
135-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
138                android:value="androidx.startup" />
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
139        </provider>
140
141        <service
141-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
142            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
142-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
144            android:enabled="@bool/enable_system_alarm_service_default"
144-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
145            android:exported="false" />
145-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
146        <service
146-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
147            android:name="androidx.work.impl.background.systemjob.SystemJobService"
147-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
148            android:directBootAware="false"
148-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
149            android:enabled="@bool/enable_system_job_service_default"
149-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
150            android:exported="true"
150-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
151            android:permission="android.permission.BIND_JOB_SERVICE" />
151-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
152        <service
152-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
153            android:name="androidx.work.impl.foreground.SystemForegroundService"
153-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
155            android:enabled="@bool/enable_system_foreground_service_default"
155-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
156            android:exported="false" />
156-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
157
158        <receiver
158-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
159            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
159-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
161            android:enabled="true"
161-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
162            android:exported="false" />
162-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
163        <receiver
163-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
164-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
169                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
169-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
169-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
170                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
170-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
170-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
174            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
174-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
176            android:enabled="false"
176-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
179                <action android:name="android.intent.action.BATTERY_OKAY" />
179-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
179-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
180                <action android:name="android.intent.action.BATTERY_LOW" />
180-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
180-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
184-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
186            android:enabled="false"
186-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
189                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
189-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
189-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
190                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
190-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
190-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
194            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
194-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
196            android:enabled="false"
196-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
197            android:exported="false" >
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
198            <intent-filter>
198-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
199                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
199-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
199-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
200            </intent-filter>
201        </receiver>
202        <receiver
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
203            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
205            android:enabled="false"
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
207            <intent-filter>
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
208                <action android:name="android.intent.action.BOOT_COMPLETED" />
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
209                <action android:name="android.intent.action.TIME_SET" />
209-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
209-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
210                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
210-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
210-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
211            </intent-filter>
212        </receiver>
213        <receiver
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
214            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
216            android:enabled="@bool/enable_system_alarm_service_default"
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
217            android:exported="false" >
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
218            <intent-filter>
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
219                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
220            </intent-filter>
221        </receiver>
222        <receiver
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
223            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
225            android:enabled="true"
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
226            android:exported="true"
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
227            android:permission="android.permission.DUMP" >
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
228            <intent-filter>
228-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
229                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
229-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
230            </intent-filter>
231        </receiver>
232
233        <uses-library
233-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
234            android:name="android.ext.adservices"
234-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
235            android:required="false" />
235-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
236
237        <receiver
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
238            android:name="androidx.profileinstaller.ProfileInstallReceiver"
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
239            android:directBootAware="false"
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
240            android:enabled="true"
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
241            android:exported="true"
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
242            android:permission="android.permission.DUMP" >
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
244                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
247                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
250                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
253                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
254            </intent-filter>
255        </receiver>
256
257        <service
257-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
258            android:name="androidx.room.MultiInstanceInvalidationService"
258-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
259            android:directBootAware="true"
259-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
260            android:exported="false" />
260-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
261    </application>
262
263</manifest>
