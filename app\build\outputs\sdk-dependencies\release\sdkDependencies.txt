# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.20"
  }
  digests {
    sha256: "\343\230\266ywb\'\030\277\030\377\231\2679\307\331\332\006\0173\373E\212.% 2!\301j\360\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.20"
  }
  digests {
    sha256: "\257\036\304\f;\225\032\375\314\f*\001s\307\270\027c\305(\034-[\257\277\n\205D\242L]\314\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\270\353\231}\3244\327\016\271|\275m\023\225\2663|gqq|z\315Y\361\034\bS\363\206\002\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.7"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.8.7"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.7"
  }
  digests {
    sha256: "p64RZg\261\306\276\252\253O\230\r\372\v\254\337~\024=\v]Q\365\254\r\224\004@\002\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.7"
  }
  digests {
    sha256: "\327\206\345\346\367\233\217\253\002o\t\242\324\256=\341\327(u\367dZk{\344\217\035\232\263\204sK"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\266\347\205\350S+\025Y!1\005c\251\017\322b\265\332\317id\256\352l\273\344\022\254\035\245\224`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\200\277\n\217\351\247\233\223\243\030\203+\303i\000\363\253\213\325\223\037\371\312\016\3150\207\020\016\037\a\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.7.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.7.1"
  }
  digests {
    sha256: "{\177HC}s\354\224ii\0247\366\274V\322\3433\333\3562\213\020ZHd\332c\210[q\205"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.7.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.7.1"
  }
  digests {
    sha256: "e\376\207\223n\3023\317q\362\027\321c\330{\250\177E\356*U\253\372\342\220\\\302\356\346\232\270\310"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.7"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\263\323\234]\272^ ~\364\307\235I\032vP\001\224\305\352ht\025\177\005\241_\001y,\f\304\347"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.7"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\347\347\004?f\260wcuc\377VJ\177\306\316\344a\322\204\240\362H\\\005\n!C\253\275\211 "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.8.7"
  }
  digests {
    sha256: "\024P\347.\352.\310\b\260\211\271j\275\307s\312\003\244\024>&\177z\255\331\256\271\247\303\232\376\361"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\032\316\273P\337/\f\a\363y\325\342:(\367yOp\357\262\266\3104\254\353\350\303\224xN\346\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.7"
  }
  digests {
    sha256: "do*B\322\210K\0020\217\261\2307\b\236&i\203_\342\034\246\245\266\006f\325\353\224\320\201\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.7"
  }
  digests {
    sha256: "b\340v\242\336-e\n#\277yIT\031\370\2445z\330\351\262\215\200\023\030\320\374\335n5\372\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.10.1"
  }
  digests {
    sha256: "\370\232\361\262l\314\203F48|\205|-\324\364eM7e\b\220\003\234R|/\355\a\333ja"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\341\377\306_\202S\2552Es\367~:T\321\'ZF\351\177\031\372*\ap\212l\264\265\314N\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\351\265Vc3\214A\247P\003\215\314\317G)\035\226\027\251}-\240\203\377\200@1\331\256G\331I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.7.0"
  }
  digests {
    sha256: "#w\245\257\262\345\237\206It\355\224\336\371\305\252\204\022\350\bBi\274[\247\027\364\262b\251I\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\023!\245\a\310`_\202\005\r\345\353\256\216\r\a*\271\267\302z\2355^\016>>\2222q\203T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\213!Q{1\022\265\267\2178\322\034X\034\230\033U*\b!\206_\355\234\257s6\ro\217q\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\017\3544z\021o\003\016z\272\213\035A\b8S2\023@\234\222\271 $\253\363_\205V\200M\371"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\360\244v0\264\340\f\033\231{?\347o\331n \253\354\211\372\325F\321\221c\332\030\324\240\210\241S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.7.0"
  }
  digests {
    sha256: "c\022v\330\b\303>5\253\362\371\027\300r\372\034\321z\225\222\233\210<t\311B\250\322\2072\233\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.7.0"
  }
  digests {
    sha256: "C\234\233\310\335\225B\352| \205\031\a{\215G\304\306\215>i\237\216\226\037\270\v\302\225[\273\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "-\302#Y\336\203\355\002Y\250\267\203O\026\331\207\330*\220\307\031~\253\264Mr\313\332\306{\370,"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\323\340\371\200\376\231\216\314\333\360\351x\354\346\324)\345\346\255\211\225z2\303\360r8)\356\355\206$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.09.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.3.0"
  }
  digests {
    sha256: "\363\033\333\202\005\207\300\231\271Fb\346\352\315B\217B\311!\036\326\fE\261\027uc\016\311\356\247\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\222]$c\310\vG\263\360L\253oI8\025>\017]\225\217\020\255\371B(\327\036\340n\037\304\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.7.0"
  }
  digests {
    sha256: "\247W8\270\3379\204\343\376\036\216I\304\265\373:\002\'\372\006 \323T\016\335\217jZw\231\307\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "22.6.0"
  }
  digests {
    sha256: "\\Q\223\252V?\0236\313\251\016\032\237y|\250\275\237\225\214\277u%\364~i\b5\335f\3327"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.4.0"
  }
  digests {
    sha256: "\341\220o\204Q/\036\245\344\311&\333\271\025x\232\330\367IO\244\356\232\322E\026?v\030\\\354\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.11.0"
  }
  digests {
    sha256: "r\034\271\030B\264o\240V\204}\020MR%\310\270\341\350\266\"c\271\223\005\036\036Z\0017\267\354"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-base"
    version: "22.6.0"
  }
  digests {
    sha256: "\"J\246\'\3464\316k\220\245\334=\322\326>\255,\216Iqm\233u\264\a\205KV\365\227\320\337"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.2.0"
  }
  digests {
    sha256: "\357C\353\374d\035qH\025CROF\321&y;L\265{\364f\304\337L\344=\f\265\341\033\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.0.0"
  }
  digests {
    sha256: "e\3352\327\037\346Z2\347y\211\246\317\261\255\t0p8\222\177\202\247@\307a\021b\320\265\030\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\345\b\306\225H\224\2237M\224+\367\264\356\002\253\367W\035%\252\304\306\"\345}l\325\315)\353s"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\224\002D,\334ZC\317b\373\024\370\317\230\3063B\324\331\331\270\005\310\003<l\367\350\002t\232\301"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.0.0"
  }
  digests {
    sha256: "v\277\373|\357\277x\a\224\330\201p\002\332\321V/>\'\300\251\367F\326$\001\310\355\263\n\356\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-lite"
    version: "22.6.0"
  }
  digests {
    sha256: "\376$\373W\301P7\324\231\375#O\373*\337\355Iq\212\257S\273\253\022&\363\003\241{b\371\352"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\210\202>\000\272\232\256m\306Cj\355Y(\300p\326\212b\302X\035\351ne\\\250o\003\016r\251"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.2.5"
  }
  digests {
    sha256: "$\245T\233ynC\3437Q=)\b\255\254g\364SP\331\251\v\312~.a i!@\273\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.2.5"
  }
  digests {
    sha256: "+\023\r\324\241\323\331\033g\001\3553\tm8\237\001\304\374\021\227\247\254\326\271\027$\335\305\254\374\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.1.0"
  }
  digests {
    sha256: "\206ss\177\333.\373\255\221\256\256\355\031\'\353\262\222\022\323j\206}\223\271c\234\200i\001\237\212\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.1.0"
  }
  digests {
    sha256: "\203A\377\t-``\326*\a\"\177)#qU\377\363o\261o\226\311_\275\232\210N7]\271\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "20.1.2"
  }
  digests {
    sha256: "\315\221\rE\276~\276;p\311\244\301l\233\356e\350t\201\025\355@\0226C\230O5\351\033_8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "20.1.2"
  }
  digests {
    sha256: "\356\224}z\017\342,Z2\357l\233oz\020z\352j\320pDu\207N\326is\bC\034{\276"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "2.1.0"
  }
  digests {
    sha256: "kZ\350\336\302g\364\016\365\262\213\323\375\255\231\361\221\321\225N`\000\352^x\316^\341\032Y\240o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.1"
  }
  digests {
    sha256: "\340N\315Ou\317E\312,7\273S\373 \224Z\234Z2,\025\341l$\031pmg/\260\020\016"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.0"
  }
  digests {
    sha256: "?0\211PA\374\352F\334Z\301Q\243|\r\350\243\241i\265 \000zS\320\227\201\235\363\205\337`"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.1"
  }
  digests {
    sha256: "\361\006\333H\306\314\372\216\023\025\247\255\304J\354\320/\3675^\263\372}\315\313\250\302\203\250\353\026\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.8.0"
  }
  digests {
    sha256: "\241\230\035\252\213\370B6\231|C\261\204\3400\000\231\"\v\211\347Ke\201\353F\213\243|tEy"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 48
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 51
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 15
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 49
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 46
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 46
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 25
  library_dep_index: 2
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 23
}
library_dependencies {
  library_index: 26
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 2
}
library_dependencies {
  library_index: 27
  library_dep_index: 6
  library_dep_index: 21
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 30
  library_dep_index: 46
}
library_dependencies {
  library_index: 28
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 46
}
library_dependencies {
  library_index: 29
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 46
}
library_dependencies {
  library_index: 30
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 46
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 6
  library_dep_index: 33
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 46
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 0
  library_dep_index: 33
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 46
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 46
}
library_dependencies {
  library_index: 41
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 46
}
library_dependencies {
  library_index: 42
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 27
  library_dep_index: 31
  library_dep_index: 28
  library_dep_index: 41
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 46
}
library_dependencies {
  library_index: 43
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 29
  library_dep_index: 39
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 30
  library_dep_index: 46
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 0
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 44
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 43
}
library_dependencies {
  library_index: 47
  library_dep_index: 6
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
}
library_dependencies {
  library_index: 49
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 14
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 55
  library_dep_index: 5
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 53
  library_dep_index: 0
  library_dep_index: 54
}
library_dependencies {
  library_index: 53
  library_dep_index: 54
  library_dep_index: 37
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 54
  library_dep_index: 52
}
library_dependencies {
  library_index: 54
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 17
  library_dep_index: 39
  library_dep_index: 43
  library_dep_index: 49
  library_dep_index: 44
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 0
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 53
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 57
  library_dep_index: 10
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 8
  library_dep_index: 72
  library_dep_index: 68
  library_dep_index: 31
  library_dep_index: 39
  library_dep_index: 49
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 26
  library_dep_index: 23
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 73
}
library_dependencies {
  library_index: 57
  library_dep_index: 8
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 6
  library_dep_index: 33
  library_dep_index: 60
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 55
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 64
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 64
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 8
  library_dep_index: 71
  library_dep_index: 4
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 64
  library_dep_index: 60
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 58
  library_dep_index: 60
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 69
  library_dep_index: 60
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 62
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 8
  library_dep_index: 68
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 23
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 69
  library_dep_index: 64
  library_dep_index: 60
}
library_dependencies {
  library_index: 68
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 6
  library_dep_index: 33
  library_dep_index: 4
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 64
  library_dep_index: 60
}
library_dependencies {
  library_index: 71
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 72
  library_dep_index: 5
  library_dep_index: 0
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 74
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 75
  library_dep_index: 79
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 8
  library_dep_index: 68
  library_dep_index: 4
  library_dep_index: 79
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 77
  library_dep_index: 79
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 60
  library_dep_index: 4
  library_dep_index: 77
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 62
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 23
  library_dep_index: 75
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 77
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 64
  library_dep_index: 60
  library_dep_index: 8
  library_dep_index: 4
  library_dep_index: 73
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 55
  library_dep_index: 62
  library_dep_index: 69
  library_dep_index: 83
  library_dep_index: 56
  library_dep_index: 63
  library_dep_index: 70
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 79
  library_dep_index: 84
  library_dep_index: 86
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 58
  library_dep_index: 64
  library_dep_index: 78
  library_dep_index: 74
  library_dep_index: 80
  library_dep_index: 85
  library_dep_index: 87
  library_dep_index: 67
  library_dep_index: 61
  library_dep_index: 59
  library_dep_index: 65
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 34
  library_dep_index: 36
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 52
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 77
  library_dep_index: 73
  library_dep_index: 79
  library_dep_index: 84
  library_dep_index: 86
  library_dep_index: 33
  library_dep_index: 55
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 27
  library_dep_index: 4
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
}
library_dependencies {
  library_index: 85
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 4
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 87
  library_dep_index: 10
  library_dep_index: 75
  library_dep_index: 73
  library_dep_index: 33
  library_dep_index: 60
  library_dep_index: 4
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 98
  library_dep_index: 115
  library_dep_index: 116
  library_dep_index: 125
  library_dep_index: 99
  library_dep_index: 127
  library_dep_index: 14
}
library_dependencies {
  library_index: 89
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 14
}
library_dependencies {
  library_index: 90
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 6
  library_dep_index: 13
  library_dep_index: 5
  library_dep_index: 90
  library_dep_index: 92
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 90
}
library_dependencies {
  library_index: 92
  library_dep_index: 93
  library_dep_index: 14
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
}
library_dependencies {
  library_index: 99
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 100
}
library_dependencies {
  library_index: 100
  library_dep_index: 8
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 6
  library_dep_index: 104
  library_dep_index: 39
}
library_dependencies {
  library_index: 101
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 102
  library_dep_index: 107
  library_dep_index: 108
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 16
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 114
}
library_dependencies {
  library_index: 102
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 103
  library_dep_index: 6
}
library_dependencies {
  library_index: 104
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 28
  library_dep_index: 39
}
library_dependencies {
  library_index: 105
  library_dep_index: 6
}
library_dependencies {
  library_index: 106
  library_dep_index: 6
}
library_dependencies {
  library_index: 107
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 108
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 107
}
library_dependencies {
  library_index: 109
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 107
}
library_dependencies {
  library_index: 110
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 107
}
library_dependencies {
  library_index: 111
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 107
}
library_dependencies {
  library_index: 112
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 16
}
library_dependencies {
  library_index: 113
  library_dep_index: 6
  library_dep_index: 8
}
library_dependencies {
  library_index: 114
  library_dep_index: 6
}
library_dependencies {
  library_index: 115
  library_dep_index: 99
}
library_dependencies {
  library_index: 116
  library_dep_index: 117
  library_dep_index: 98
  library_dep_index: 99
  library_dep_index: 122
  library_dep_index: 124
}
library_dependencies {
  library_index: 117
  library_dep_index: 9
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 47
  library_dep_index: 8
  library_dep_index: 118
  library_dep_index: 121
  library_dep_index: 120
  library_dep_index: 8
  library_dep_index: 41
}
library_dependencies {
  library_index: 118
  library_dep_index: 119
  library_dep_index: 120
  library_dep_index: 121
  library_dep_index: 20
}
library_dependencies {
  library_index: 119
  library_dep_index: 6
}
library_dependencies {
  library_index: 120
  library_dep_index: 6
  library_dep_index: 121
}
library_dependencies {
  library_index: 121
  library_dep_index: 6
}
library_dependencies {
  library_index: 122
  library_dep_index: 99
  library_dep_index: 123
}
library_dependencies {
  library_index: 123
  library_dep_index: 99
}
library_dependencies {
  library_index: 124
  library_dep_index: 6
  library_dep_index: 115
  library_dep_index: 99
}
library_dependencies {
  library_index: 125
  library_dep_index: 126
  library_dep_index: 99
  library_dep_index: 127
}
library_dependencies {
  library_index: 126
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 100
  library_dep_index: 99
  library_dep_index: 127
}
library_dependencies {
  library_index: 127
  library_dep_index: 99
}
library_dependencies {
  library_index: 128
  library_dep_index: 6
  library_dep_index: 8
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 37
  dependency_index: 52
  dependency_index: 81
  dependency_index: 55
  dependency_index: 62
  dependency_index: 69
  dependency_index: 82
  dependency_index: 88
  dependency_index: 128
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
