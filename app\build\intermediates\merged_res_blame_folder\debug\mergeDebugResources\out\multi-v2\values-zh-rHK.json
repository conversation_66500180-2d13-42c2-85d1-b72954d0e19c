{"logs": [{"outputFile": "com.mdmusfikurrahaman.travelessentialshub.app-mergeDebugResources-59:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7d63a424524ff7886e421a479e6ca837\\transformed\\browser-1.4.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "2742,3082,3174,3275", "endColumns": "82,91,100,92", "endOffsets": "2820,3169,3270,3363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9a42abb6d6494ce25d6213be6f37263e\\transformed\\core-1.16.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,197,296,390,484,577,670,10688", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "192,291,385,479,572,665,761,10784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a2ea4acbe4700842098c83253cdfc736\\transformed\\play-services-base-18.0.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "916,1017,1145,1260,1362,1469,1585,1687,1888,1998,2099,2228,2343,2450,2558,2613,2670", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "1012,1140,1255,1357,1464,1580,1682,1775,1993,2094,2223,2338,2445,2553,2608,2665,2737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,888,963,1030,1103,1173,1242,1317,1382", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,72,69,68,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,883,958,1025,1098,1168,1237,1312,1377,1493"}, "to": {"startLines": "9,10,30,31,32,36,37,98,99,110,111,119,120,121,122,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,842,2825,2913,3004,3368,3442,9183,9261,9992,10065,10409,10476,10549,10619,10789,10864,10929", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,72,69,68,74,64,115", "endOffsets": "837,911,2908,2999,3077,3437,3514,9256,9330,10060,10135,10471,10544,10614,10683,10859,10924,11040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3448ec15e9e04b037c5166989b571054\\transformed\\play-services-basement-18.2.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1780", "endColumns": "107", "endOffsets": "1883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16518290575c9189fbb528717b99852d\\transformed\\play-services-ads-22.6.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,242,285,334,394,455,525,584,648,698,769,823,895,951,983,1026,1056,1086,1120,1160,1192", "endColumns": "38,42,48,59,60,69,58,63,49,70,53,71,55,31,42,29,29,33,39,31,55", "endOffsets": "241,284,333,393,454,524,583,647,697,768,822,894,950,982,1025,1055,1085,1119,1159,1191,1247"}, "to": {"startLines": "95,96,97,100,101,102,103,104,105,106,107,108,109,112,113,114,115,116,117,118,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9040,9083,9130,9335,9399,9464,9538,9601,9669,9723,9798,9856,9932,10140,10176,10223,10257,10291,10329,10373,11203", "endColumns": "42,46,52,63,64,73,62,67,53,74,57,75,59,35,46,33,33,37,43,35,59", "endOffsets": "9078,9125,9178,9394,9459,9533,9596,9664,9718,9793,9851,9927,9987,10171,10218,10252,10286,10324,10368,10404,11258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "11045,11126", "endColumns": "80,76", "endOffsets": "11121,11198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4042,4125,4207,4296,4376,4458,4555,4649,4742,4835,4919,5015,5111,5206,5314,5394,5486", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4037,4120,4202,4291,4371,4453,4550,4644,4737,4830,4914,5010,5106,5201,5309,5389,5481,5571"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3519,3622,3724,3828,3929,4020,4109,4214,4319,4424,4540,4622,4718,4802,4890,4995,5108,5209,5317,5423,5531,5647,5752,5854,5959,6065,6150,6245,6350,6459,6549,6651,6749,6858,6972,7072,7163,7236,7326,7415,7506,7589,7671,7760,7840,7922,8019,8113,8206,8299,8383,8479,8575,8670,8778,8858,8950", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "3617,3719,3823,3924,4015,4104,4209,4314,4419,4535,4617,4713,4797,4885,4990,5103,5204,5312,5418,5526,5642,5747,5849,5954,6060,6145,6240,6345,6454,6544,6646,6744,6853,6967,7067,7158,7231,7321,7410,7501,7584,7666,7755,7835,7917,8014,8108,8201,8294,8378,8474,8570,8665,8773,8853,8945,9035"}}]}]}