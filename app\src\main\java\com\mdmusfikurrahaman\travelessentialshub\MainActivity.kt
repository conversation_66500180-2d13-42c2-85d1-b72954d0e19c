package com.mdmusfikurrahaman.travelessentialshub

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.webkit.WebView
import android.webkit.WebViewClient
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.AdSize
import com.google.android.gms.ads.AdView
import com.mdmusfikurrahaman.travelessentialshub.ui.theme.TravelEssentialsHubTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        // Pass null instead of savedInstanceState to prevent restoring previous state
        super.onCreate(null)
        enableEdgeToEdge()

        try {
            // Initialize MobileAds
            MobileAds.initialize(this) { initializationStatus ->
                // Handle initialization completion
            }

            // Load native ad
            loadNativeAd()

            setContent {
                TravelEssentialsHubTheme {
                    Surface(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        MainContent()
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // Prevent saving instance state
    override fun onSaveInstanceState(outState: Bundle) {
        // Don't call super to prevent saving state
        // super.onSaveInstanceState(outState)
    }

    private fun loadNativeAd() {
        try {
            val adLoader = AdLoader.Builder(this, "ca-app-pub-2281902770675036/4880599032")
                .forNativeAd { nativeAd: NativeAd ->
                    // Handle the loaded Native Ad here
                }
                .withNativeAdOptions(NativeAdOptions.Builder().build())
                .build()

            adLoader.loadAd(com.google.android.gms.ads.AdRequest.Builder().build())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

@Composable
fun MainContent() {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // WebView takes most of the space
        WebViewContent(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )

        // Banner ad at the bottom
        BannerAdView(
            modifier = Modifier
                .fillMaxWidth()
                .height(60.dp)
        )
    }
}

@SuppressLint("SetJavaScriptEnabled")
@Suppress("DEPRECATION")
@Composable
fun WebViewContent(modifier: Modifier = Modifier) {
    val context = LocalContext.current

    AndroidView(
        factory = { ctx ->
            WebView(ctx).apply {
                webViewClient = object : WebViewClient() {
                    // Handle external links
                    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                        request?.url?.let { uri ->
                            // If the URL is not a local asset file, open it in external browser
                            if (!uri.toString().startsWith("file:///android_asset")) {
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                context.startActivity(intent)
                                return true
                            }
                        }
                        return false
                    }

                    // For older Android versions
                    @Deprecated("Deprecated in Java")
                    override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                        url?.let {
                            // If the URL is not a local asset file, open it in external browser
                            if (!it.startsWith("file:///android_asset")) {
                                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(it))
                                context.startActivity(intent)
                                return true
                            }
                        }
                        return false
                    }

                    override fun onReceivedError(
                        view: WebView?,
                        errorCode: Int,
                        description: String?,
                        failingUrl: String?
                    ) {
                        super.onReceivedError(view, errorCode, description, failingUrl)
                        // Handle error here
                    }
                }

                // Clear cache and history to start fresh
                clearCache(true)
                clearHistory()

                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = false // Disable DOM storage to prevent state saving
                    loadWithOverviewMode = true
                    useWideViewPort = true
                    builtInZoomControls = true
                    displayZoomControls = false
                    setSupportZoom(true)
                    mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                    cacheMode = WebSettings.LOAD_NO_CACHE // Prevent caching
                    saveFormData = false // Don't save form data
                }

                // Load the URL fresh each time
                loadUrl("file:///android_asset/index.html") // Make sure this file exists in assets folder
            }
        },
        modifier = Modifier.fillMaxSize()
    )
}