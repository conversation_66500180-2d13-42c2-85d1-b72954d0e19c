<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>BD Flight Finder</title>
    <style>
        :root {
          --primary: #006A4E; /* Bangladesh green */
          --secondary: #f5f7fa;
          --accent: #ffffff;
          --text-dark: #1f2d3d;
          --text-light: #6c7a89;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
          font-family: 'Segoe UI', sans-serif;
          background-color: var(--secondary);
          margin: 0;
          color: var(--text-dark);
        }

        header {
          background: linear-gradient(to right, #006A4E, #009E49); /* Bangladesh flag colors */
          padding: 2rem 1rem;
          text-align: center;
          color: var(--accent);
          box-shadow: 0 4px 10px var(--shadow);
        }

        header h1 {
          margin: 0;
          font-size: 2rem;
        }

        header p {
          margin-top: 0.5rem;
          font-size: 1rem;
          opacity: 0.9;
        }

        .search-box {
          margin: 1rem auto;
          text-align: center;
        }

        .search-box input {
          width: 90%;
          max-width: 400px;
          padding: 0.75rem 1rem;
          border: none;
          border-radius: 50px;
          box-shadow: 0 2px 8px var(--shadow);
          font-size: 1rem;
        }

        .container {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 1rem;
          padding: 1rem;
        }

        .card {
          background: white;
          border-radius: 16px;
          box-shadow: 0 4px 12px var(--shadow);
          padding: 1.2rem;
          width: 100%;
          max-width: 280px;
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .card h3 {
          margin-top: 0;
          font-size: 1.2rem;
          color: var(--primary);
        }

        .card a {
          display: inline-block;
          margin-top: 0.5rem;
          color: #007BFF;
          word-wrap: break-word;
          font-size: 0.95rem;
          text-decoration: none;
          transition: color 0.3s;
        }

        .card a:hover {
          color: #0056b3;
          text-decoration: underline;
        }

        @media (max-width: 600px) {
          .card {
            max-width: 90%;
          }

          header h1 {
            font-size: 1.5rem;
          }
        }
    </style>
</head>
<body>

<header>
    <h1>✈️ BD Flight Finder</h1>
    <p>Find the best flight deals in Bangladesh</p>
</header>

<div class="search-box">
    <label for="searchInput"></label><input type="text" id="searchInput" placeholder="Search flight companies..." onkeyup="filterCards()" />
</div>

<div class="container" id="linkContainer">
    <!-- Dynamic cards will be loaded here -->
</div>

<script>
    const links = [
      { name: "Flight Expert", url: "https://www.flightexpert.com" },
      { name: "Gozayaan", url: "https://gozayaan.com" },
      { name: "ShareTrip", url: "https://sharetrip.net" },
      { name: "Amy BD", url: "https://www.amybd.com" },
      { name: "Biman Bangladesh Airlines", url: "https://www.biman-airlines.com" },
      { name: "Trip", url: "https://trip.tpx.gr" }
    ];

    const container = document.getElementById("linkContainer");

    function renderCards(data) {
      container.innerHTML = "";
      data.forEach(link => {
        const card = document.createElement("div");
        card.className = "card";
        card.innerHTML = `
          <h3>${link.name}</h3>
          <a href="${link.url}" target="_blank" rel="noopener noreferrer">${link.name}</a>
        `;
        container.appendChild(card);
      });
    }

    function filterCards() {
      const query = document.getElementById("searchInput").value.toLowerCase();
      const filtered = links.filter(link => link.name.toLowerCase().includes(query));
      renderCards(filtered);
    }

    renderCards(links);
</script>

</body>
</html>
