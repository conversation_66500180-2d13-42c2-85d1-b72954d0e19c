{"options": {"hasObfuscationDictionary": false, "hasClassObfuscationDictionary": false, "hasPackageObfuscationDictionary": false, "keepAttributes": {"isAnnotationDefaultKept": true, "isEnclosingMethodKept": true, "isExceptionsKept": false, "isInnerClassesKept": true, "isLocalVariableTableKept": false, "isLocalVariableTypeTableKept": false, "isMethodParametersKept": false, "isPermittedSubclassesKept": false, "isRuntimeInvisibleAnnotationsKept": true, "isRuntimeInvisibleParameterAnnotationsKept": true, "isRuntimeInvisibleTypeAnnotationsKept": true, "isRuntimeVisibleAnnotationsKept": true, "isRuntimeVisibleParameterAnnotationsKept": true, "isRuntimeVisibleTypeAnnotationsKept": true, "isSignatureKept": true, "isSourceDebugExtensionKept": false, "isSourceDirKept": false, "isSourceFileKept": true, "isStackMapTableKept": false}, "isAccessModificationEnabled": true, "isFlattenPackageHierarchyEnabled": false, "isObfuscationEnabled": true, "isOptimizationsEnabled": true, "isProGuardCompatibilityModeEnabled": false, "isProtoLiteOptimizationEnabled": false, "isRepackageClassesEnabled": false, "isShrinkingEnabled": true, "apiModeling": {}, "minApiLevel": "24", "isDebugModeEnabled": false}, "baselineProfileRewriting": {}, "compilation": {"buildTimeNs": 154084484100, "numberOfThreads": 4}, "dexFiles": [{"checksum": "9b64bbd5e0db97276b6e8f5de463636fd4cf5577dc1b62fffbb758542644d0c8", "startup": false}, {"checksum": "016f68ef5d46e82fb52fe13e65b1bfb0f7b4fdde9be717b2e4c48076ebd39a5f", "startup": false}], "stats": {"noObfuscationPercentage": 71.86, "noOptimizationPercentage": 72.02, "noShrinkingPercentage": 71.92}, "featureSplits": {"featureSplits": [{"dexFiles": []}], "isolatedSplits": false}, "resourceOptimization": {"isOptimizedShrinkingEnabled": false}, "version": "8.9.35"}