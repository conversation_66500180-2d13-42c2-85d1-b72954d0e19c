{"logs": [{"outputFile": "com.mdmusfikurrahaman.travelessentialshub.app-mergeDebugResources-59:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ed0c0b337a87b416c3485c34c7ebaa86\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "126", "startColumns": "4", "startOffsets": "7743", "endColumns": "49", "endOffsets": "7788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cb713f0fd3eb49283725a6ce38a8e6a9\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7586", "endColumns": "42", "endOffsets": "7624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f9b2f3ac4e57c2a361a436e16b1cb4e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "125", "startColumns": "4", "startOffsets": "7689", "endColumns": "53", "endOffsets": "7738"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TravelEssentialsHub\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endColumns": "56", "endOffsets": "109"}, "to": {"startLines": "25", "startColumns": "4", "startOffsets": "1784", "endColumns": "56", "endOffsets": "1836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fcdaf28699cc4114e6d32da17e246a20\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "96", "startColumns": "4", "startOffsets": "6165", "endColumns": "65", "endOffsets": "6226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3448ec15e9e04b037c5166989b571054\\transformed\\play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "128,150", "startColumns": "4,4", "startOffsets": "7846,9869", "endColumns": "67,166", "endOffsets": "7909,10031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,97,98,127,140,141,161,162,163,167,168,239,240,261,262,270,271,272,273,275,276,277,283,299,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4068,4127,4186,4246,4306,4366,4426,4486,4546,4606,4666,4726,4786,4845,4905,4965,5025,5085,5145,5205,5265,5325,5385,5445,5504,5564,5624,5683,5742,5801,5860,5919,5978,6052,6110,6231,6282,7793,8710,8775,11230,11296,11397,11670,11722,17022,17084,19071,19121,19531,19577,19623,19665,19776,19823,19859,20229,21209,21320", "endLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,97,98,127,140,141,161,162,163,167,168,239,240,261,262,270,271,272,273,275,276,277,285,301,305", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "4122,4181,4241,4301,4361,4421,4481,4541,4601,4661,4721,4781,4840,4900,4960,5020,5080,5140,5200,5260,5320,5380,5440,5499,5559,5619,5678,5737,5796,5855,5914,5973,6047,6105,6160,6277,6332,7841,8770,8824,11291,11392,11450,11717,11777,17079,17133,19116,19170,19572,19618,19660,19700,19818,19854,19944,20336,21315,21510"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TravelEssentialsHub\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "96", "endOffsets": "148"}, "to": {"startLines": "325", "startColumns": "4", "startOffsets": "22671", "endColumns": "95", "endOffsets": "22762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5e612104df3321f05aa271207b528de3\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "271,336,406,470", "endColumns": "64,69,63,60", "endOffsets": "331,401,465,526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\601e1d2d8cfb8d2af03b51eefc908f6d\\transformed\\play-services-ads-lite-22.6.0\\res\\values\\values.xml", "from": {"startLines": "4,14", "startColumns": "0,0", "startOffsets": "167,661", "endLines": "11,20", "endColumns": "8,20", "endOffsets": "560,836"}, "to": {"startLines": "317,331", "startColumns": "4,4", "startOffsets": "22273,23051", "endLines": "324,337", "endColumns": "8,20", "endOffsets": "22666,23226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e44e2169711aa2308a9cd97797e3ea24\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "2,328,354,360", "startColumns": "4,4,4,4", "startOffsets": "150,22906,23735,23946", "endLines": "2,330,359,443", "endColumns": "60,12,24,24", "endOffsets": "206,23046,23941,28457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fdc69ff0892fcc2f8cb1a2edba8ee609\\transformed\\activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "103,124", "startColumns": "4,4", "startOffsets": "6528,7629", "endColumns": "41,59", "endOffsets": "6565,7684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "129,169,170,171,172,173,174,175,176,177,178,181,182,183,184,185,186,187,188,189,190,191,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,286,296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7914,11782,11870,11956,12037,12121,12190,12255,12338,12444,12530,12650,12704,12773,12834,12903,12992,13087,13161,13258,13351,13449,13598,13689,13777,13873,13971,14035,14103,14190,14284,14351,14423,14495,14596,14705,14781,14850,14898,14964,15028,15102,15159,15216,15288,15338,15392,15463,15534,15604,15673,15731,15807,15878,15952,16038,16088,16158,20341,21056", "endLines": "129,169,170,171,172,173,174,175,176,177,180,181,182,183,184,185,186,187,188,189,190,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,295,298", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7982,11865,11951,12032,12116,12185,12250,12333,12439,12525,12645,12699,12768,12829,12898,12987,13082,13156,13253,13346,13444,13593,13684,13772,13868,13966,14030,14098,14185,14279,14346,14418,14490,14591,14700,14776,14845,14893,14959,15023,15097,15154,15211,15283,15333,15387,15458,15529,15599,15668,15726,15802,15873,15947,16033,16083,16153,16218,21051,21204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\229fb45d41e6a7dc95d863a72eae7b4c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "99,102", "startColumns": "4,4", "startOffsets": "6337,6461", "endColumns": "53,66", "endOffsets": "6386,6523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d4c5419693f837eebddb887747f95a1b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "131", "startColumns": "4", "startOffsets": "8057", "endColumns": "82", "endOffsets": "8135"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TravelEssentialsHub\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "10,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "691,1973,2020,2067,2114,2159,2204", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "728,2015,2062,2109,2154,2199,2241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "278,279", "startColumns": "4,4", "startOffsets": "19949,20005", "endColumns": "55,54", "endOffsets": "20000,20055"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\TravelEssentialsHub\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "58", "endOffsets": "70"}, "to": {"startLines": "132", "startColumns": "4", "startOffsets": "8140", "endColumns": "58", "endOffsets": "8194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7d63a424524ff7886e421a479e6ca837\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "11,12,13,14,34,35,160,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "733,791,857,920,2246,2317,11162,11455,11522,11601", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "786,852,915,977,2312,2384,11225,11517,11596,11665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a2ea4acbe4700842098c83253cdfc736\\transformed\\play-services-base-18.0.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "17,18,19,20,21,22,23,24,142,143,144,145,146,147,148,149,151,152,153,154,155,156,157,158,159,539,552", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1113,1203,1283,1373,1463,1543,1624,1704,8829,8934,9115,9240,9347,9527,9650,9766,10036,10224,10329,10510,10635,10810,10958,11021,11083,31798,32113", "endLines": "17,18,19,20,21,22,23,24,142,143,144,145,146,147,148,149,151,152,153,154,155,156,157,158,159,551,570", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "1198,1278,1368,1458,1538,1619,1699,1779,8929,9110,9235,9342,9522,9645,9761,9864,10219,10324,10505,10630,10805,10953,11016,11078,11157,32108,32525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9a42abb6d6494ce25d6213be6f37263e\\transformed\\core-1.16.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,100,101,105,106,107,108,114,124,159,180,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4484,4542,4603,4666,4723,4774,4832,4882,4943,5000,5066,5100,5135,5170,5240,5307,5379,5448,5517,5591,5663,5751,5822,5939,6140,6250,6451,6580,6652,6719,6922,7223,9029,9710,10392", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99,100,104,105,106,107,113,123,158,179,212,218", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4479,4537,4598,4661,4718,4769,4827,4877,4938,4995,5061,5095,5130,5165,5235,5302,5374,5443,5512,5586,5658,5746,5817,5934,6135,6245,6446,6575,6647,6714,6917,7218,9024,9705,10387,10554"}, "to": {"startLines": "3,8,9,15,16,26,27,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,100,101,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,130,133,134,135,136,137,138,139,274,306,307,311,312,316,326,327,338,344,444,479,500,533", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "211,531,603,982,1047,1841,1910,2389,2459,2527,2599,2669,2730,2804,2877,2938,2999,3061,3125,3187,3248,3316,3416,3476,3542,3615,3684,3741,3793,3855,3927,4003,6391,6426,6570,6625,6688,6743,6801,6857,6915,6976,7039,7096,7147,7205,7255,7316,7373,7439,7473,7508,7987,8199,8266,8338,8407,8476,8550,8622,19705,21515,21632,21833,21943,22144,22767,22839,23231,23434,28462,30268,30949,31631", "endLines": "3,8,9,15,16,26,27,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,100,101,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,130,133,134,135,136,137,138,139,274,306,310,311,315,316,326,327,343,353,478,499,532,538", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,55,57,60,62,56,50,57,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "266,598,686,1042,1108,1905,1968,2454,2522,2594,2664,2725,2799,2872,2933,2994,3056,3120,3182,3243,3311,3411,3471,3537,3610,3679,3736,3788,3850,3922,3998,4063,6421,6456,6620,6683,6738,6796,6852,6910,6971,7034,7091,7142,7200,7250,7311,7368,7434,7468,7503,7538,8052,8261,8333,8402,8471,8545,8617,8705,19771,21627,21828,21938,22139,22268,22834,22901,23429,23730,30263,30944,31626,31793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16518290575c9189fbb528717b99852d\\transformed\\play-services-ads-22.6.0\\res\\values\\values.xml", "from": {"startLines": "4,7,10,13,15,17,19,21,23,25,27,29,31,33,34,35,36,37,38,39,40", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "170,417,666,942,1081,1238,1450,1633,1847,2018,2248,2431,2650,2835,2873,2946,2980,3015,3064,3128,3163", "endLines": "6,9,12,14,16,18,20,22,24,26,28,30,32,33,34,35,36,37,38,39,42", "endColumns": "11,11,11,20,27,51,29,58,17,80,24,81,48,37,72,33,34,48,63,34,11", "endOffsets": "416,665,941,1080,1237,1449,1632,1846,2017,2247,2430,2649,2834,2872,2945,2979,3014,3063,3127,3162,3327"}, "to": {"startLines": "230,233,236,241,243,245,247,249,251,253,255,257,259,263,264,265,266,267,268,269,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16223,16479,16737,17138,17281,17442,17658,17845,18063,18238,18472,18659,18882,19175,19217,19294,19332,19371,19424,19492,20060", "endLines": "232,235,238,242,244,246,248,250,252,254,256,258,260,263,264,265,266,267,268,269,282", "endColumns": "11,11,11,20,27,51,29,58,17,80,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "16474,16732,17017,17276,17437,17653,17840,18058,18233,18467,18654,18877,19066,19212,19289,19327,19366,19419,19487,19526,20224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fbec202e4d898202eedcb38956f20fdc\\transformed\\core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7543", "endColumns": "42", "endOffsets": "7581"}}]}]}