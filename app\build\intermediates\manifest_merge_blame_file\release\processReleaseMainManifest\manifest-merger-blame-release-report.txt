1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdmusfikurrahaman.travelessentialshub"
4    android:versionCode="4"
5    android:versionName="2.2" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:25:5-79
12-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:25:22-76
13    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
13-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:5-79
13-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:26:22-76
14    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
14-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
14-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
15-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
15-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
16-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
16-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
17    <queries>
17-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
18
19        <!-- For browser content -->
20        <intent>
20-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
21            <action android:name="android.intent.action.VIEW" />
21-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:13-65
21-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:39:21-62
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
23-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
24
25            <data android:scheme="https" />
25-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
25-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
26        </intent>
27        <!-- End of browser content -->
28        <!-- For CustomTabsService -->
29        <intent>
29-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
30            <action android:name="android.support.customtabs.action.CustomTabsService" />
30-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
30-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
31        </intent>
32        <!-- End of CustomTabsService -->
33    </queries>
34
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
35-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
36-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
37
38    <permission
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
39        android:name="com.mdmusfikurrahaman.travelessentialshub.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.mdmusfikurrahaman.travelessentialshub.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:7:5-36:19
45        android:allowBackup="false"
45-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:8:9-36
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:9:9-65
48        android:extractNativeLibs="false"
49        android:fullBackupContent="@xml/backup_rules"
49-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:10:9-54
50        android:icon="@mipmap/ic_launcher"
50-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:11:9-43
51        android:label="@string/app_name"
51-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:12:9-41
52        android:roundIcon="@mipmap/ic_launcher_round"
52-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:13:9-54
53        android:supportsRtl="true"
53-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:14:9-35
54        android:theme="@style/Theme.TravelEssentialsHub" >
54-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:15:9-57
55        <meta-data
55-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:18:9-20:69
56            android:name="com.google.android.gms.ads.APPLICATION_ID"
56-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:19:13-69
57            android:value="ca-app-pub-2281902*********~1234567890" />
57-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:20:13-67
58
59        <activity
59-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:22:9-35:20
60            android:name="com.mdmusfikurrahaman.travelessentialshub.MainActivity"
60-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:23:13-41
61            android:alwaysRetainTaskState="false"
61-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:27:13-50
62            android:clearTaskOnLaunch="true"
62-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:28:13-45
63            android:exported="true"
63-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:24:13-36
64            android:label="@string/app_name"
64-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:25:13-45
65            android:noHistory="true"
65-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:26:13-37
66            android:theme="@style/Theme.TravelEssentialsHub" >
66-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:29:13-61
67            <intent-filter>
67-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:30:13-34:29
68                <action android:name="android.intent.action.MAIN" />
68-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:31:17-69
68-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:31:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:33:17-77
70-->C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\src\main\AndroidManifest.xml:33:27-74
71            </intent-filter>
72        </activity>
73        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
74        <activity
74-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
75            android:name="com.google.android.gms.ads.AdActivity"
75-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
76            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
76-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
77            android:exported="false"
77-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
78            android:theme="@android:style/Theme.Translucent" />
78-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
79
80        <provider
80-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
81            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
81-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
82            android:authorities="com.mdmusfikurrahaman.travelessentialshub.mobileadsinitprovider"
82-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
83            android:exported="false"
83-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
84            android:initOrder="100" />
84-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
85
86        <service
86-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
87            android:name="com.google.android.gms.ads.AdService"
87-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
88            android:enabled="true"
88-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
89            android:exported="false" />
89-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
90
91        <activity
91-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
92            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
92-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
93            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
93-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
94            android:exported="false" />
94-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
95        <activity
95-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
96            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
96-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
97            android:excludeFromRecents="true"
97-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
98            android:exported="false"
98-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
99            android:launchMode="singleTask"
99-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
100            android:taskAffinity=""
100-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
101            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
101-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
102
103        <property
103-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:90:9-92:62
104            android:name="android.adservices.AD_SERVICES_CONFIG"
104-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:91:13-65
105            android:resource="@xml/gma_ad_services_config" />
105-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\AndroidManifest.xml:92:13-59
106
107        <activity
107-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
108            android:name="com.google.android.gms.common.api.GoogleApiActivity"
108-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
109            android:exported="false"
109-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
110            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
110-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
111
112        <meta-data
112-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
113            android:name="com.google.android.gms.version"
113-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
114            android:value="@integer/google_play_services_version" />
114-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
115
116        <provider
116-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
117            android:name="androidx.startup.InitializationProvider"
117-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
118            android:authorities="com.mdmusfikurrahaman.travelessentialshub.androidx-startup"
118-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
119            android:exported="false" >
119-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
120            <meta-data
120-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
121                android:name="androidx.work.WorkManagerInitializer"
121-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
122                android:value="androidx.startup" />
122-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
123            <meta-data
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.emoji2.text.EmojiCompatInitializer"
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
125                android:value="androidx.startup" />
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <service
134-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
135            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
135-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
136            android:directBootAware="false"
136-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
137            android:enabled="@bool/enable_system_alarm_service_default"
137-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
138            android:exported="false" />
138-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
139        <service
139-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
140            android:name="androidx.work.impl.background.systemjob.SystemJobService"
140-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
141            android:directBootAware="false"
141-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
142            android:enabled="@bool/enable_system_job_service_default"
142-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
143            android:exported="true"
143-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
144            android:permission="android.permission.BIND_JOB_SERVICE" />
144-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
145        <service
145-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
146            android:name="androidx.work.impl.foreground.SystemForegroundService"
146-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
148            android:enabled="@bool/enable_system_foreground_service_default"
148-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
149            android:exported="false" />
149-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
150
151        <receiver
151-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
152            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
152-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
154            android:enabled="true"
154-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
155            android:exported="false" />
155-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
156        <receiver
156-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
157            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
157-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
159            android:enabled="false"
159-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
160            android:exported="false" >
160-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
161            <intent-filter>
161-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
162                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
162-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
162-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
163                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
163-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
163-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
164            </intent-filter>
165        </receiver>
166        <receiver
166-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
167            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
167-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
169            android:enabled="false"
169-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
170            android:exported="false" >
170-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
171            <intent-filter>
171-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
172                <action android:name="android.intent.action.BATTERY_OKAY" />
172-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
172-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
173                <action android:name="android.intent.action.BATTERY_LOW" />
173-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
173-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
179            android:enabled="false"
179-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
180            android:exported="false" >
180-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
181            <intent-filter>
181-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
182                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
182-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
182-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
183                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
184            </intent-filter>
185        </receiver>
186        <receiver
186-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
187            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
188            android:directBootAware="false"
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
189            android:enabled="false"
189-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
190            android:exported="false" >
190-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
191            <intent-filter>
191-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
192                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
192-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
192-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
193            </intent-filter>
194        </receiver>
195        <receiver
195-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
196            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
196-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
197            android:directBootAware="false"
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
198            android:enabled="false"
198-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
199            android:exported="false" >
199-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
200            <intent-filter>
200-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
201                <action android:name="android.intent.action.BOOT_COMPLETED" />
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
202                <action android:name="android.intent.action.TIME_SET" />
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
203                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
204            </intent-filter>
205        </receiver>
206        <receiver
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
207            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
208            android:directBootAware="false"
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
209            android:enabled="@bool/enable_system_alarm_service_default"
209-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
210            android:exported="false" >
210-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
211            <intent-filter>
211-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
212                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
213            </intent-filter>
214        </receiver>
215        <receiver
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
216            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
217            android:directBootAware="false"
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
218            android:enabled="true"
218-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
219            android:exported="true"
219-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
220            android:permission="android.permission.DUMP" >
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
221            <intent-filter>
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
222                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
223            </intent-filter>
224        </receiver>
225
226        <uses-library
226-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
227            android:name="android.ext.adservices"
227-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
228            android:required="false" />
228-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
229
230        <receiver
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
231            android:name="androidx.profileinstaller.ProfileInstallReceiver"
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
232            android:directBootAware="false"
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
233            android:enabled="true"
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
234            android:exported="true"
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
235            android:permission="android.permission.DUMP" >
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
237                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
240                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
243                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
246                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
247            </intent-filter>
248        </receiver>
249
250        <service
250-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
251            android:name="androidx.room.MultiInstanceInvalidationService"
251-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
252            android:directBootAware="true"
252-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
253            android:exported="false" />
253-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
254    </application>
255
256</manifest>
