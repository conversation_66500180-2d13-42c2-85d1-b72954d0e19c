{"logs": [{"outputFile": "com.mdmusfikurrahaman.travelessentialshub.app-mergeReleaseResources-55:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,987,1068,1140,1215,1290,1365,1445,1511", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,982,1063,1135,1210,1285,1360,1440,1506,1624"}, "to": {"startLines": "9,10,30,31,32,36,37,98,99,110,111,119,120,121,122,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "835,928,3381,3473,3572,3978,4056,10495,10583,11587,11666,12148,12220,12295,12370,12546,12626,12692", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "923,1006,3468,3567,3655,4051,4149,10578,10662,11661,11742,12215,12290,12365,12440,12621,12687,12805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3448ec15e9e04b037c5166989b571054\\transformed\\play-services-basement-18.2.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2022", "endColumns": "150", "endOffsets": "2168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9a42abb6d6494ce25d6213be6f37263e\\transformed\\core-1.16.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,608,706,12445", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "198,300,400,501,603,701,830,12541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a2ea4acbe4700842098c83253cdfc736\\transformed\\play-services-base-18.0.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1011,1118,1291,1421,1530,1677,1806,1919,2173,2335,2444,2617,2749,2902,3063,3128,3194", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "1113,1286,1416,1525,1672,1801,1914,2017,2330,2439,2612,2744,2897,3058,3123,3189,3271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "12810,12897", "endColumns": "86,86", "endOffsets": "12892,12979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16518290575c9189fbb528717b99852d\\transformed\\play-services-ads-22.6.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,246,292,344,410,479,583,659,766,820,953,1012,1135,1224,1268,1351,1389,1428,1473,1556,1597", "endColumns": "46,45,51,65,68,103,75,106,53,132,58,122,88,43,82,37,38,44,82,40,55", "endOffsets": "245,291,343,409,478,582,658,765,819,952,1011,1134,1223,1267,1350,1388,1427,1472,1555,1596,1652"}, "to": {"startLines": "95,96,97,100,101,102,103,104,105,106,107,108,109,112,113,114,115,116,117,118,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10338,10389,10439,10667,10737,10810,10918,10998,11109,11167,11304,11367,11494,11747,11795,11882,11924,11967,12016,12103,12984", "endColumns": "50,49,55,69,72,107,79,110,57,136,62,126,92,47,86,41,42,48,86,44,59", "endOffsets": "10384,10434,10490,10732,10805,10913,10993,11104,11162,11299,11362,11489,11582,11790,11877,11919,11962,12011,12098,12143,13039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,291,407,526,623,724,842,980,1104,1247,1332,1435,1525,1622,1734,1855,1963,2098,2235,2366,2532,2658,2773,2892,3012,3103,3199,3318,3454,3556,3659,3765,3897,4035,4146,4245,4321,4418,4519,4631,4716,4804,4903,4983,5067,5167,5266,5361,5459,5545,5646,5744,5846,5961,6041,6143", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "167,286,402,521,618,719,837,975,1099,1242,1327,1430,1520,1617,1729,1850,1958,2093,2230,2361,2527,2653,2768,2887,3007,3098,3194,3313,3449,3551,3654,3760,3892,4030,4141,4240,4316,4413,4514,4626,4711,4799,4898,4978,5062,5162,5261,5356,5454,5540,5641,5739,5841,5956,6036,6138,6234"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4154,4271,4390,4506,4625,4722,4823,4941,5079,5203,5346,5431,5534,5624,5721,5833,5954,6062,6197,6334,6465,6631,6757,6872,6991,7111,7202,7298,7417,7553,7655,7758,7864,7996,8134,8245,8344,8420,8517,8618,8730,8815,8903,9002,9082,9166,9266,9365,9460,9558,9644,9745,9843,9945,10060,10140,10242", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "4266,4385,4501,4620,4717,4818,4936,5074,5198,5341,5426,5529,5619,5716,5828,5949,6057,6192,6329,6460,6626,6752,6867,6986,7106,7197,7293,7412,7548,7650,7753,7859,7991,8129,8240,8339,8415,8512,8613,8725,8810,8898,8997,9077,9161,9261,9360,9455,9553,9639,9740,9838,9940,10055,10135,10237,10333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7d63a424524ff7886e421a479e6ca837\\transformed\\browser-1.4.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3276,3660,3761,3875", "endColumns": "104,100,113,102", "endOffsets": "3376,3756,3870,3973"}}]}]}