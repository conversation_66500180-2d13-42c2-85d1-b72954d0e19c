<lint-module
    format="1"
    dir="C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app"
    name=":app"
    type="APP"
    maven="Travel Essentials Hub:app:unspecified"
    agpVersion="8.9.2"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
