{"logs": [{"outputFile": "com.mdmusfikurrahaman.travelessentialshub.app-mergeReleaseResources-55:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7d63a424524ff7886e421a479e6ca837\\transformed\\browser-1.4.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3327,3719,3818,3934", "endColumns": "102,98,115,101", "endOffsets": "3425,3813,3929,4031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1017,1104,1177,1253,1330,1406,1484,1552", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,1012,1099,1172,1248,1325,1401,1479,1547,1669"}, "to": {"startLines": "9,10,30,31,32,36,37,98,99,110,111,119,120,121,122,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,934,3430,3526,3629,4036,4122,10739,10832,11787,11872,12349,12422,12498,12575,12752,12830,12898", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,75,76,75,77,67,121", "endOffsets": "929,1018,3521,3624,3714,4117,4205,10827,10911,11867,11954,12417,12493,12570,12646,12825,12893,13015"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "13020,13110", "endColumns": "89,89", "endOffsets": "13105,13195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a2ea4acbe4700842098c83253cdfc736\\transformed\\play-services-base-18.0.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1023,1130,1291,1424,1532,1674,1805,1922,2194,2369,2478,2647,2782,2951,3106,3170,3238", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "1125,1286,1419,1527,1669,1800,1917,2023,2364,2473,2642,2777,2946,3101,3165,3233,3322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3448ec15e9e04b037c5166989b571054\\transformed\\play-services-basement-18.2.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2028", "endColumns": "165", "endOffsets": "2189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9a42abb6d6494ce25d6213be6f37263e\\transformed\\core-1.16.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,405,506,613,721,12651", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "198,300,400,501,608,716,831,12747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,309,424,549,658,758,875,1013,1131,1278,1364,1462,1556,1657,1776,1900,2003,2141,2272,2410,2593,2725,2844,2971,3091,3186,3285,3406,3541,3643,3757,3863,3998,4143,4252,4355,4438,4533,4627,4737,4827,4914,5025,5105,5191,5286,5390,5481,5579,5668,5775,5877,5977,6130,6210,6315", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "177,304,419,544,653,753,870,1008,1126,1273,1359,1457,1551,1652,1771,1895,1998,2136,2267,2405,2588,2720,2839,2966,3086,3181,3280,3401,3536,3638,3752,3858,3993,4138,4247,4350,4433,4528,4622,4732,4822,4909,5020,5100,5186,5281,5385,5476,5574,5663,5770,5872,5972,6125,6205,6310,6409"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4210,4337,4464,4579,4704,4813,4913,5030,5168,5286,5433,5519,5617,5711,5812,5931,6055,6158,6296,6427,6565,6748,6880,6999,7126,7246,7341,7440,7561,7696,7798,7912,8018,8153,8298,8407,8510,8593,8688,8782,8892,8982,9069,9180,9260,9346,9441,9545,9636,9734,9823,9930,10032,10132,10285,10365,10470", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "4332,4459,4574,4699,4808,4908,5025,5163,5281,5428,5514,5612,5706,5807,5926,6050,6153,6291,6422,6560,6743,6875,6994,7121,7241,7336,7435,7556,7691,7793,7907,8013,8148,8293,8402,8505,8588,8683,8777,8887,8977,9064,9175,9255,9341,9436,9540,9631,9729,9818,9925,10027,10127,10280,10360,10465,10564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16518290575c9189fbb528717b99852d\\transformed\\play-services-ads-22.6.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,247,298,357,421,487,582,667,759,811,927,986,1096,1188,1234,1307,1343,1379,1433,1509,1550", "endColumns": "47,50,58,63,65,94,84,91,51,115,58,109,91,45,72,35,35,53,75,40,55", "endOffsets": "246,297,356,420,486,581,666,758,810,926,985,1095,1187,1233,1306,1342,1378,1432,1508,1549,1605"}, "to": {"startLines": "95,96,97,100,101,102,103,104,105,106,107,108,109,112,113,114,115,116,117,118,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10569,10621,10676,10916,10984,11054,11153,11242,11338,11394,11514,11577,11691,11959,12009,12086,12126,12166,12224,12304,13200", "endColumns": "51,54,62,67,69,98,88,95,55,119,62,113,95,49,76,39,39,57,79,44,59", "endOffsets": "10616,10671,10734,10979,11049,11148,11237,11333,11389,11509,11572,11686,11782,12004,12081,12121,12161,12219,12299,12344,13255"}}]}]}