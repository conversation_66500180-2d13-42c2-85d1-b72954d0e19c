<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Travel Essentials Hub</title>
    <style>
        :root {
          --primary: #4a90e2;
          --secondary: #f5f7fa;
          --accent: #ffffff;
          --text-dark: #1f2d3d;
          --text-light: #6c7a89;
          --shadow: rgba(0, 0, 0, 0.1);
        }

        body {
          font-family: 'Segoe UI', sans-serif;
          background-color: var(--secondary);
          margin: 0;
          color: var(--text-dark);
        }

        header {
          background: linear-gradient(to right, #4a90e2, #007BFF);
          padding: 2rem 1rem;
          text-align: center;
          color: var(--accent);
          box-shadow: 0 4px 10px var(--shadow);
        }

        header h1 {
          margin: 0;
          font-size: 2rem;
        }

        header p {
          margin-top: 0.5rem;
          font-size: 1rem;
          opacity: 0.9;
        }

        .search-box {
          margin: 1rem auto;
          text-align: center;
        }

        .search-box input {
          width: 90%;
          max-width: 400px;
          padding: 0.75rem 1rem;
          border: none;
          border-radius: 50px;
          box-shadow: 0 2px 8px var(--shadow);
          font-size: 1rem;
        }

        .container {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 1rem;
          padding: 1rem;
        }

        .card {
          background: white;
          border-radius: 16px;
          box-shadow: 0 4px 12px var(--shadow);
          padding: 1.2rem;
          width: 100%;
          max-width: 280px;
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .card h3 {
          margin-top: 0;
          font-size: 1.2rem;
          color: var(--primary);
        }

        .card a {
          display: inline-block;
          margin-top: 0.5rem;
          color: #007BFF;
          word-wrap: break-word;
          font-size: 0.95rem;
          text-decoration: none;
          transition: color 0.3s;
        }

        .card a:hover {
          color: #0056b3;
          text-decoration: underline;
        }

        @media (max-width: 600px) {
          .card {
            max-width: 90%;
          }

          header h1 {
            font-size: 1.5rem;
          }
        }
    </style>
</head>
<body>

<header>
    <h1>🌍 Travel Essentials Hub</h1>
    <p>Your one-stop platform for all travel needs</p>
</header>

<div class="search-box">
    <label for="searchInput"></label><input type="text" id="searchInput" placeholder="Search (e.g., SIM, taxi, hotel)" onkeyup="filterCards()" />
</div>

<div class="container" id="linkContainer">
    <!-- Dynamic cards will be loaded here -->
</div>

<script>
    const links = [
      { name: "Bus Booking", url: "https://12go.tp.st/YyL7kRXs" },
      { name: "SIM (Airalo)", url: "https://airalo.tp.st/PcjgCFxB" },
      { name: "Air Ticket (Aviasales)", url: "https://aviasales.tp.st/AnfgUe8k" },
      { name: "Bike Booking", url: "https://bikesbooking.tp.st/WbhauhLQ" },
      { name: "Flight Compensation (Compensair)", url: "https://compensair.tp.st/v6HXGhYt" },
      { name: "Global SIM (Drimsim)", url: "https://drimsim.tp.st/ZIjTGS1D" },
      { name: "Car Rental (Economy Bookings)", url: "https://economybookings.tp.st/g01HL6XT" },
      { name: "Transfers (GetTransfer)", url: "https://gettransfer.tp.st/1ky9fvme" },
      { name: "Hotels (Hotellook)", url: "https://hotellook.tp.st/wTh0Ohty" },
      { name: "Taxi & Ride (inDrive)", url: "https://indrive.tp.st/NCUWaHgX" },
      { name: "Flights & Deals (Kiwi)", url: "https://kiwi.tp.st/4Ig8CYEX" },
      { name: "Taxi (Kiwitaxi)", url: "https://kiwitaxi.tp.st/j4S7e1jv" },
      { name: "Car Rental (Qeeq)", url: "https://qeeq.tp.st/S4Tk9g7M" },
      { name: "Luggage Storage", url: "https://radicalstorage.tp.st/hXTmlsb6" },
      { name: "Hotels (SnapTravel)", url: "https://snaptravel.tp.st/PnD2pf88" },
      { name: "Attractions Tickets", url: "https://tiqets.tp.st/qEEyxrx9" },
      { name: "Travel Booking (Trip)", url: "https://trip.tp.st/5f3tZN8w" },
      { name: "Flight Cashback (WayAway)", url: "https://wayaway.tp.st/gXL5pFyk" },
      { name: "Tours & Audio Guides", url: "https://wegotrip.tp.st/EtpKZoR3" },
      { name: "Other Deals", url: "https://tp.st/9Wy8YQ8Z" },
      { name: "SIM (Yesim)", url: "https://yesim.tp.st/ojs10MwG" }
    ];

    const container = document.getElementById("linkContainer");

    function renderCards(data) {
      container.innerHTML = "";
      data.forEach(link => {
        const card = document.createElement("div");
        card.className = "card";
        card.innerHTML = `
          <h3>${link.name}</h3>
          <a href="${link.url}" target="_blank" rel="noopener noreferrer">${link.name}</a>
        `;
        container.appendChild(card);
      });
    }

    function filterCards() {
      const query = document.getElementById("searchInput").value.toLowerCase();
      const filtered = links.filter(link => link.name.toLowerCase().includes(query));
      renderCards(filtered);
    }

    renderCards(links);
</script>

</body>
</html>
