{"logs": [{"outputFile": "com.mdmusfikurrahaman.travelessentialshub.app-mergeDebugResources-59:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,402,519,618,715,829,971,1089,1228,1313,1415,1507,1605,1723,1845,1952,2094,2238,2370,2546,2672,2793,2913,3032,3125,3225,3348,3486,3585,3691,3797,3941,4086,4193,4292,4375,4470,4564,4675,4760,4844,4945,5025,5108,5207,5307,5402,5504,5591,5695,5794,5899,6030,6110,6214", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "169,285,397,514,613,710,824,966,1084,1223,1308,1410,1502,1600,1718,1840,1947,2089,2233,2365,2541,2667,2788,2908,3027,3120,3220,3343,3481,3580,3686,3792,3936,4081,4188,4287,4370,4465,4559,4670,4755,4839,4940,5020,5103,5202,5302,5397,5499,5586,5690,5789,5894,6025,6105,6209,6304"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4106,4225,4341,4453,4570,4669,4766,4880,5022,5140,5279,5364,5466,5558,5656,5774,5896,6003,6145,6289,6421,6597,6723,6844,6964,7083,7176,7276,7399,7537,7636,7742,7848,7992,8137,8244,8343,8426,8521,8615,8726,8811,8895,8996,9076,9159,9258,9358,9453,9555,9642,9746,9845,9950,10081,10161,10265", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "4220,4336,4448,4565,4664,4761,4875,5017,5135,5274,5359,5461,5553,5651,5769,5891,5998,6140,6284,6416,6592,6718,6839,6959,7078,7171,7271,7394,7532,7631,7737,7843,7987,8132,8239,8338,8421,8516,8610,8721,8806,8890,8991,9071,9154,9253,9353,9448,9550,9637,9741,9840,9945,10076,10156,10260,10355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3448ec15e9e04b037c5166989b571054\\transformed\\play-services-basement-18.2.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2026", "endColumns": "149", "endOffsets": "2171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9a42abb6d6494ce25d6213be6f37263e\\transformed\\core-1.16.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,123", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,406,504,609,721,12427", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "198,300,401,499,604,716,835,12523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7d63a424524ff7886e421a479e6ca837\\transformed\\browser-1.4.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "29,33,34,35", "startColumns": "4,4,4,4", "startOffsets": "3233,3609,3713,3818", "endColumns": "104,103,104,107", "endOffsets": "3333,3708,3813,3921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\16518290575c9189fbb528717b99852d\\transformed\\play-services-ads-22.6.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,248,302,359,426,495,594,674,764,814,920,972,1092,1179,1221,1300,1337,1374,1430,1509,1545", "endColumns": "48,53,56,66,68,98,79,89,49,105,51,119,86,41,78,36,36,55,78,35,55", "endOffsets": "247,301,358,425,494,593,673,763,813,919,971,1091,1178,1220,1299,1336,1373,1429,1508,1544,1600"}, "to": {"startLines": "95,96,97,100,101,102,103,104,105,106,107,108,109,112,113,114,115,116,117,118,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10360,10413,10471,10700,10771,10844,10947,11031,11125,11179,11289,11345,11469,11726,11772,11855,11896,11937,11997,12080,12963", "endColumns": "52,57,60,70,72,102,83,93,53,109,55,123,90,45,82,40,40,59,82,39,59", "endOffsets": "10408,10466,10527,10766,10839,10942,11026,11120,11174,11284,11340,11464,11555,11767,11850,11891,11932,11992,12075,12115,13018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,985,1068,1141,1218,1298,1375,1452,1518", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,980,1063,1136,1213,1293,1370,1447,1513,1630"}, "to": {"startLines": "9,10,30,31,32,36,37,98,99,110,111,119,120,121,122,124,125,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,932,3338,3430,3526,3926,4012,10532,10619,11560,11643,12120,12193,12270,12350,12528,12605,12671", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,76,79,76,76,65,116", "endOffsets": "927,1013,3425,3521,3604,4007,4101,10614,10695,11638,11721,12188,12265,12345,12422,12600,12666,12783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a2ea4acbe4700842098c83253cdfc736\\transformed\\play-services-base-18.0.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1018,1128,1285,1417,1524,1661,1787,1916,2176,2320,2427,2595,2724,2865,3033,3094,3156", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "1123,1280,1412,1519,1656,1782,1911,2021,2315,2422,2590,2719,2860,3028,3089,3151,3228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "127,128", "startColumns": "4,4", "startOffsets": "12788,12874", "endColumns": "85,88", "endOffsets": "12869,12958"}}]}]}