# The proguard configuration file for the following section is C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
-keep class androidx.core.app.CoreComponentFactory { <init>(); }
-keep class androidx.profileinstaller.ProfileInstallReceiver { <init>(); }
-keep class androidx.room.MultiInstanceInvalidationService { <init>(); }
-keep class androidx.startup.InitializationProvider { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy { <init>(); }
-keep class androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.RescheduleReceiver { <init>(); }
-keep class androidx.work.impl.background.systemalarm.SystemAlarmService { <init>(); }
-keep class androidx.work.impl.background.systemjob.SystemJobService { <init>(); }
-keep class androidx.work.impl.diagnostics.DiagnosticsReceiver { <init>(); }
-keep class androidx.work.impl.foreground.SystemForegroundService { <init>(); }
-keep class androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver { <init>(); }
-keep class com.google.android.gms.ads.AdActivity { <init>(); }
-keep class com.google.android.gms.ads.AdService { <init>(); }
-keep class com.google.android.gms.ads.MobileAdsInitProvider { <init>(); }
-keep class com.google.android.gms.ads.NotificationHandlerActivity { <init>(); }
-keep class com.google.android.gms.ads.OutOfContextTestingActivity { <init>(); }
-keep class com.google.android.gms.common.api.GoogleApiActivity { <init>(); }
-keep class com.mdmusfikurrahaman.travelessentialshub.MainActivity { <init>(); }
-keep class androidx.browser.browseractions.BrowserActionsFallbackMenuView { <init>(android.content.Context, android.util.AttributeSet); }


# End of content from C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\build\intermediates\aapt_proguard_file\release\processReleaseResources\aapt_rules.txt
# The proguard configuration file for the following section is C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.9.2
# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
#
# Starting with version 2.2 of the Android plugin for Gradle, this file is distributed together with
# the plugin and unpacked at build-time. The files in $ANDROID_HOME are no longer maintained and
# will be ignored by new version of the Android plugin for Gradle.

# Optimizations: If you don't want to optimize, use the proguard-android.txt configuration file
# instead of this one, which turns off the optimization flags.
-allowaccessmodification

# Preserve some attributes that may be required for reflection.
-keepattributes AnnotationDefault,
                EnclosingMethod,
                InnerClasses,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations,
                Signature

-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService
-keep public class com.google.android.vending.licensing.ILicensingService
-dontnote com.android.vending.licensing.ILicensingService
-dontnote com.google.vending.licensing.ILicensingService
-dontnote com.google.android.vending.licensing.ILicensingService

# For native methods, see https://www.guardsquare.com/manual/configuration/examples#native
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# Keep setters in Views so that animations can still work.
-keepclassmembers public class * extends android.view.View {
    void set*(***);
    *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick.
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}

# For enumeration classes, see https://www.guardsquare.com/manual/configuration/examples#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Preserve annotated Javascript interface methods.
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# The support libraries contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version. We know about them, and they are safe.
-dontnote android.support.**
-dontnote androidx.**
-dontwarn android.support.**
-dontwarn androidx.**

# Understand the @Keep support annotation.
-keep class android.support.annotation.Keep

-keep @android.support.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @android.support.annotation.Keep <init>(...);
}

# These classes are duplicated between android.jar and org.apache.http.legacy.jar.
-dontnote org.apache.http.**
-dontnote android.net.http.**

# These classes are duplicated between android.jar and core-lambda-stubs.jar.
-dontnote java.lang.invoke.**

# End of content from C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.9.2
# The proguard configuration file for the following section is C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\proguard-rules.pro
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Keep WebView JavaScript interfaces
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep WebView related classes
-keep public class android.webkit.WebView
-keep public class android.webkit.WebViewClient

# Preserve the line number information for debugging stack traces
-keepattributes SourceFile,LineNumberTable

# Keep file names but hide the original source file name
-renamesourcefileattribute SourceFile

# Keep Compose related classes
-keep class androidx.compose.** { *; }
-keep class androidx.activity.compose.** { *; }

# Keep Google Ads related classes
-keep class com.google.android.gms.ads.** { *; }

# General Android rules
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep the app's entry points
-keep class com.mdmusfikurrahaman.travelessentialshub.MainActivity { *; }
# End of content from C:\Users\<USER>\AndroidStudioProjects\TravelEssentialsHub\app\proguard-rules.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1722e60b3e06639d9e378fb2f4330d38\transformed\ui-release\proguard.txt
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# We supply these as stubs and are able to link to them at runtime
# because they are hidden public classes in Android. We don't want
# R8 to complain about them not being there during optimization.
-dontwarn android.view.RenderNode
-dontwarn android.view.DisplayListCanvas
-dontwarn android.view.HardwareCanvas

-keepclassmembers class androidx.compose.ui.platform.ViewLayerContainer {
    protected void dispatchGetDisplayList();
}

-keepclassmembers class androidx.compose.ui.platform.AndroidComposeView {
    android.view.View findViewByAccessibilityIdTraversal(int);
}

# Users can create Modifier.Node instances that implement multiple Modifier.Node interfaces,
# so we cannot tell whether two modifier.node instances are of the same type without using
# reflection to determine the class type. See b/265188224 for more context.
-keep,allowshrinking class * extends androidx.compose.ui.node.ModifierNodeElement

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1722e60b3e06639d9e378fb2f4330d38\transformed\ui-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16518290575c9189fbb528717b99852d\transformed\play-services-ads-22.6.0\proguard.txt
-keep public class com.google.android.gms.ads.internal.ClientApi {
  <init>();
}

# When built for Android API level < 30, Proguard warns that it can't find
# android.telephony.TelephonyDisplayInfo (since it was added only in API level
# 30). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.telephony.TelephonyDisplayInfo

# When built for Android API level < 30, Proguard warns that it can't find
# android.view.Surface#setFrameRate(float, int) (since it was added only in API
# level 30). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.view.Surface

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.ApplicationMediaCapabilities (since it was added only in API
# level 31). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.ApplicationMediaCapabilities

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.MediaFeature (since it was added only in API level 31). But,
# all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.MediaFeature

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.ApplicationMediaCapabilities$Builder (since it was added only in
# API level 31). But, all its usages are guarded by runtime checks of the API
# level. Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.ApplicationMediaCapabilities$Builder

# When built for Android API level < 31, Proguard warns that it can't find
# android.media.MediaFeature$HdrType (since it was added only in API level 31).
# But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.MediaFeature$HdrType

# When built for Android API level < 32, Proguard warns that it can't find
# android.media.AudioAttributes$Builder (since it was added only in API level
# 32). But, all its usages are guarded by runtime checks of the API level.
# Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.media.AudioAttributes$Builder

# When built for Android API level < 33, Proguard warns that it can't find
# android.adservices.measurement.MeasurementManager (since it was added only
# in API level 33). But, all its usages are guarded by runtime checks of the
# API level. Hence, it is safe to suppress Proguard's warnings.
-dontwarn android.adservices.measurement.MeasurementManager

# When built for Android API level < 33, Proguard warns that it can't find
# javax.lang.model.element.Modifier (since it was added only in API level 33).
# But, all its usages are guarded by runtime checks of the API level. Hence, it
# is safe to suppress Proguard's warnings.
-dontwarn javax.lang.model.element.Modifier

# These are checked at runtime for whether they exist, so it is fine if the API level doesn't include these.
-dontwarn android.content.pm.ApkChecksum
-dontwarn android.content.pm.PackageManager$OnChecksumsReadyListener
# Only for the requestChecksums method, but sadly -dontwarn can't take just a single method.
-dontwarn android.content.pm.PackageManager

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.ads.zzgwm {
  <fields>;
}

# Auto-generated proguard rule(s) with obfuscated symbol
-dontwarn com.google.android.gms.ads.internal.util.zzy


# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16518290575c9189fbb528717b99852d\transformed\play-services-ads-22.6.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\707058a669b0a47327c431e033fd0495\transformed\webkit-1.8.0\proguard.txt
# Copyright 2018 The Chromium Authors
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# We need to avoid obfuscating the support library boundary interface because
# this API is shared with the Android Support Library.
# Note that we only 'keep' the package org.chromium.support_lib_boundary itself,
# any sub-packages of that package can still be obfuscated.
-keep public class org.chromium.support_lib_boundary.* { public *; }

# Copyright (C) 2018 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Prevent WebViewClientCompat from being renamed, since chromium depends on this name.
-keepnames public class androidx.webkit.WebViewClientCompat

# Prevent ProcessGlobalConfig and member sProcessGlobalConfig from being renamed, since chromium
# depends on this name.
-keepnames public class androidx.webkit.ProcessGlobalConfig {
    private static final *** sProcessGlobalConfig;
}
# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\707058a669b0a47327c431e033fd0495\transformed\webkit-1.8.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\proguard.txt
# Keep implementations of the AdMob mediation adapter interfaces. Adapters for
# third party ad networks implement these interfaces and are invoked by the
# AdMob SDK via reflection.

-keep class * implements com.google.android.gms.ads.mediation.MediationAdapter {
  public *;
}
-keep class * implements com.google.ads.mediation.MediationAdapter {
  public *;
}
-keep class * implements com.google.android.gms.ads.mediation.customevent.CustomEvent {
  public *;
}
-keep class * implements com.google.ads.mediation.customevent.CustomEvent {
  public *;
}
-keep class * extends com.google.android.gms.ads.mediation.MediationAdNetworkAdapter {
  public *;
}
-keep class * extends com.google.android.gms.ads.mediation.Adapter {
  public *;
}

# Keep classes used for offline ads created by reflection. WorkManagerUtil is
# created reflectively by callers within GMSCore and OfflineNotificationPoster
# is created reflectively by WorkManager.
-keep class com.google.android.gms.ads.internal.util.WorkManagerUtil {
  public *;
}
-keep class com.google.android.gms.ads.internal.offline.buffering.OfflineNotificationPoster {
  public *;
}
-keep class com.google.android.gms.ads.internal.offline.buffering.OfflinePingSender {
  public *;
}

# Keeps the entry for full SDK to access via reflection.
-keep class com.google.android.gms.ads.internal.client.LiteSdkInfo {
  public *;
}

# Keeps the entry for first party plugins to access via reflection.
-keep class com.google.android.gms.ads.MobileAds {
  private void setPlugin(java.lang.String);
}

# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.ads.zzgwm {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\601e1d2d8cfb8d2af03b51eefc908f6d\transformed\play-services-ads-lite-22.6.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\proguard.txt
# b/35135904 Ensure that proguard will not strip the mResultGuardian.
-keepclassmembers class com.google.android.gms.common.api.internal.BasePendingResult {
  com.google.android.gms.common.api.internal.BasePendingResult$ReleasableResultGuardian mResultGuardian;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcc293139f9968384ab202fc791f3bcb\transformed\play-services-tasks-18.0.1\proguard.txt


# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcc293139f9968384ab202fc791f3bcb\transformed\play-services-tasks-18.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzkc {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6888a83d2d6e0170a1a0d14f096875d7\transformed\play-services-measurement-base-20.1.2\proguard.txt
# We keep all fields for every generated proto file as the runtime uses
# reflection over them that ProGuard cannot detect. Without this keep
# rule, fields may be removed that would cause runtime failures.
-keepclassmembers class * extends com.google.android.gms.internal.measurement.zzkc {
  <fields>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6888a83d2d6e0170a1a0d14f096875d7\transformed\play-services-measurement-base-20.1.2\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\proguard.txt
# Needed when building against pre-Marshmallow SDK.
-dontwarn android.security.NetworkSecurityPolicy

# Needed when building against Marshmallow SDK.
-dontwarn android.app.Notification

# Protobuf has references not on the Android boot classpath
-dontwarn sun.misc.Unsafe
-dontwarn libcore.io.Memory

# Annotations used during internal SDK shrinking.
-dontwarn com.google.android.apps.common.proguard.UsedBy*
-dontwarn com.google.android.apps.common.proguard.SideEffectFree

# Annotations referenced by the SDK but whose definitions are contained in
# non-required dependencies.
-dontwarn javax.annotation.**
-dontwarn org.checkerframework.**
-dontwarn com.google.errorprone.annotations.**
-dontwarn org.jspecify.nullness.NullMarked

# Proguard flags for consumers of the Google Play services SDK
# https://developers.google.com/android/guides/setup#add_google_play_services_to_your_project

# Keep SafeParcelable NULL value, needed for reflection by DowngradeableSafeParcel
-keepclassmembers public class com.google.android.gms.common.internal.safeparcel.SafeParcelable {
    public static final *** NULL;
}

# Needed for Parcelable/SafeParcelable classes & their creators to not get renamed, as they are
# found via reflection.
-keep class com.google.android.gms.common.internal.ReflectedParcelable
-keepnames class * implements com.google.android.gms.common.internal.ReflectedParcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final *** CREATOR;
}

# Keep the classes/members we need for client functionality.
-keep @interface android.support.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep androidX equivalent of above android.support to allow Jetification.
-keep @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class *
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <fields>;
}
-keepclasseswithmembers class * {
  @androidx.annotation.Keep <methods>;
}

# Keep the names of classes/members we need for client functionality.
-keep @interface com.google.android.gms.common.annotation.KeepName
-keepnames @com.google.android.gms.common.annotation.KeepName class *
-keepclassmembernames class * {
  @com.google.android.gms.common.annotation.KeepName *;
}

# Keep Dynamite API entry points
-keep @interface com.google.android.gms.common.util.DynamiteApi
-keep @com.google.android.gms.common.util.DynamiteApi public class * {
  public <fields>;
  public <methods>;
}



# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\proguard.txt
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
# Keep all constructors on ListenableWorker, Worker (also marked with @Keep)
-keep public class * extends androidx.work.ListenableWorker {
    public <init>(...);
}
# We need to keep WorkerParameters for the ListenableWorker constructor
-keep class androidx.work.WorkerParameters

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ab9a914a66e5faecca1870fcb3deb2a\transformed\graphics-path-1.0.1\proguard.txt
-keepclasseswithmembers class androidx.graphics.path.** {
    native <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ab9a914a66e5faecca1870fcb3deb2a\transformed\graphics-path-1.0.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44e2169711aa2308a9cd97797e3ea24\transformed\coordinatorlayout-1.0.0\proguard.txt
# Copyright (C) 2016 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# CoordinatorLayout resolves the behaviors of its child components with reflection.
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>();
}

# Make sure we keep annotations for CoordinatorLayout's DefaultBehavior and ViewPager's DecorView
-keepattributes *Annotation*

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e44e2169711aa2308a9cd97797e3ea24\transformed\coordinatorlayout-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\proguard.txt
# Never inline methods, but allow shrinking and obfuscation.
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.ViewCompat$Api* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.view.WindowInsetsCompat$*Impl* {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.app.NotificationCompat$*$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.os.UserHandleCompat$Api*Impl {
  <methods>;
}
-keepclassmembernames,allowobfuscation,allowshrinking class androidx.core.widget.EdgeEffectCompat$Api*Impl {
  <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f9b2f3ac4e57c2a361a436e16b1cb4e\transformed\savedstate-1.2.1\proguard.txt
# Copyright (C) 2019 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

-keepclassmembers,allowobfuscation class * implements androidx.savedstate.SavedStateRegistry$AutoRecreated {
    <init>();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f9b2f3ac4e57c2a361a436e16b1cb4e\transformed\savedstate-1.2.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\proguard.txt
# this rule is need to work properly when app is compiled with api 28, see b/142778206
-keepclassmembers class * extends androidx.lifecycle.EmptyActivityLifecycleCallbacks { *; }
# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36a45e9cac319bb3ca10fa05738d441f\transformed\lifecycle-process-2.8.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abbbd98492796918d5506972c06bdc02\transformed\lifecycle-runtime-compose-release\proguard.txt
# Workaround for https://issuetracker.google.com/issues/346808608
#
# `androidx.lifecycle.compose.LocalLifecycleOwner` will reflectively lookup for
# `androidx.compose.ui.platform.LocalLifecycleOwner` to ensure backward compatibility
# when using Lifecycle 2.8+ with Compose 1.6.
#
# We need to keep the getter if the code using this is included.
#
# We need to suppress `ShrinkerUnresolvedReference` because the `LocalComposition` is in a
# different module.
#
#noinspection ShrinkerUnresolvedReference
-if public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}
-keep public class androidx.compose.ui.platform.AndroidCompositionLocals_androidKt {
    public static *** getLocalLifecycleOwner();
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abbbd98492796918d5506972c06bdc02\transformed\lifecycle-runtime-compose-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed0c0b337a87b416c3485c34c7ebaa86\transformed\lifecycle-viewmodel-release\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>();
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed0c0b337a87b416c3485c34c7ebaa86\transformed\lifecycle-viewmodel-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb713f0fd3eb49283725a6ce38a8e6a9\transformed\lifecycle-runtime-release\proguard.txt
-keepattributes AnnotationDefault,
                RuntimeVisibleAnnotations,
                RuntimeVisibleParameterAnnotations,
                RuntimeVisibleTypeAnnotations

-keepclassmembers enum androidx.lifecycle.Lifecycle$Event {
    <fields>;
}

-keep class * implements androidx.lifecycle.GeneratedAdapter {
    <init>(...);
}

-keepclassmembers class ** {
    @androidx.lifecycle.OnLifecycleEvent *;
}

# The deprecated `android.app.Fragment` creates `Fragment` instances using reflection.
# See: b/338958225, b/341537875
-keepclasseswithmembers,allowobfuscation public class androidx.lifecycle.ReportFragment {
    public <init>();
}

# this rule is need to work properly when app is compiled with api 28, see b/142778206
# Also this rule prevents registerIn from being inlined.
-keepclassmembers class androidx.lifecycle.ReportFragment$LifecycleCallbacks { *; }

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb713f0fd3eb49283725a6ce38a8e6a9\transformed\lifecycle-runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f64bbd5fbda7c1a9cce7819514371df\transformed\lifecycle-viewmodel-savedstate-2.8.7\proguard.txt
-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.ViewModel {
    <init>(androidx.lifecycle.SavedStateHandle);
}

-keepclassmembers,allowobfuscation class * extends androidx.lifecycle.AndroidViewModel {
    <init>(android.app.Application,androidx.lifecycle.SavedStateHandle);
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f64bbd5fbda7c1a9cce7819514371df\transformed\lifecycle-viewmodel-savedstate-2.8.7\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58e0c62d172700110e5b7d2bf007e72c\transformed\core-ktx-1.16.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58e0c62d172700110e5b7d2bf007e72c\transformed\core-ktx-1.16.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\proguard.txt
# It's important that we preserve initializer names, given they are used in the AndroidManifest.xml.
-keepnames class * extends androidx.startup.Initializer

# These Proguard rules ensures that ComponentInitializers are are neither shrunk nor obfuscated,
# and are a part of the primary dex file. This is because they are discovered and instantiated
# during application startup.
-keep class * extends androidx.startup.Initializer {
    # Keep the public no-argument constructor while allowing other methods to be optimized.
    <init>();
}

-assumenosideeffects class androidx.startup.StartupLogger { public static <methods>; }

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4c5419693f837eebddb887747f95a1b\transformed\startup-runtime-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\proguard.txt
-keep class * implements androidx.versionedparcelable.VersionedParcelable
-keep public class android.support.**Parcelizer { *; }
-keep public class androidx.**Parcelizer { *; }
-keep public class androidx.versionedparcelable.ParcelImpl

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d1e03fa2a8cec5ca0751adc0bac6e04\transformed\versionedparcelable-1.1.1\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9edd6b9a9d9b8ac2ed0198f2709a45f\transformed\runtime-release\proguard.txt
-assumenosideeffects public class androidx.compose.runtime.ComposerKt {
    void sourceInformation(androidx.compose.runtime.Composer,java.lang.String);
    void sourceInformationMarkerStart(androidx.compose.runtime.Composer,int,java.lang.String);
    void sourceInformationMarkerEnd(androidx.compose.runtime.Composer);
}

# Composer's class initializer doesn't do anything but create an EMPTY object. Marking the
# initializers as having no side effects can help encourage shrinkers to merge/devirtualize Composer
# with ComposerImpl.
-assumenosideeffects public class androidx.compose.runtime.Composer {
    void <clinit>();
}
-assumenosideeffects public class androidx.compose.runtime.ComposerImpl {
    void <clinit>();
}
# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9edd6b9a9d9b8ac2ed0198f2709a45f\transformed\runtime-release\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\proguard.txt
-keep class * extends androidx.room.RoomDatabase
-dontwarn androidx.room.paging.**

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f6cf436a9cc5f4478d9d3df6beeed5\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <methods>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <fields>;
}

-keepclasseswithmembers class * {
    @androidx.annotation.Keep <init>(...);
}

-keepclassmembers,allowobfuscation class * {
  @androidx.annotation.DoNotInline <methods>;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78f6cf436a9cc5f4478d9d3df6beeed5\transformed\rules\lib\META-INF\proguard\androidx-annotations.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbec202e4d898202eedcb38956f20fdc\transformed\core-viewtree-1.0.0\proguard.txt
# Intentionally empty proguard rules to indicate this library is safe to shrink

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fbec202e4d898202eedcb38956f20fdc\transformed\core-viewtree-1.0.0\proguard.txt
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a08a1cb46641e04cdfe5811a37bb0ce1\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# When editing this file, update the following files as well:
# - META-INF/proguard/coroutines.pro
# - META-INF/com.android.tools/proguard/coroutines.pro

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembers class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}

# These classes are only required by kotlinx.coroutines.debug.AgentPremain, which is only loaded when
# kotlinx-coroutines-core is used as a Java agent, so these are not needed in contexts where ProGuard is used.
-dontwarn java.lang.instrument.ClassFileTransformer
-dontwarn sun.misc.SignalHandler
-dontwarn java.lang.instrument.Instrumentation
-dontwarn sun.misc.Signal

# Only used in `kotlinx.coroutines.internal.ExceptionsConstructor`.
# The case when it is not available is hidden in a `try`-`catch`, as well as a check for Android.
-dontwarn java.lang.ClassValue

# An annotation used for build tooling, won't be directly accessed.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a08a1cb46641e04cdfe5811a37bb0ce1\transformed\rules\lib\META-INF\com.android.tools\r8\coroutines.pro
# The proguard configuration file for the following section is C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6b33db4977d7a15fa99ccf411a79238\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# Allow R8 to optimize away the FastServiceLoader.
# Together with ServiceLoader optimization in R8
# this results in direct instantiation when loading Dispatchers.Main
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatcherLoader {
    boolean FAST_SERVICE_LOADER_ENABLED return false;
}

-assumenosideeffects class kotlinx.coroutines.internal.FastServiceLoaderKt {
    boolean ANDROID_DETECTED return true;
}

# Disable support for "Missing Main Dispatcher", since we always have Android main dispatcher
-assumenosideeffects class kotlinx.coroutines.internal.MainDispatchersKt {
    boolean SUPPORT_MISSING return false;
}

# Statically turn off all debugging facilities and assertions
-assumenosideeffects class kotlinx.coroutines.DebugKt {
    boolean getASSERTIONS_ENABLED() return false;
    boolean getDEBUG() return false;
    boolean getRECOVER_STACK_TRACES() return false;
}

# End of content from C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6b33db4977d7a15fa99ccf411a79238\transformed\rules\lib\META-INF\com.android.tools\r8-from-1.6.0\coroutines.pro
# The proguard configuration file for the following section is <unknown>

# End of content from <unknown>